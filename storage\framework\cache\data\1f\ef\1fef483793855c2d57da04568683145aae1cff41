1751348509O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:16:"App\Models\Addon":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:6:"addons";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:1;s:4:"name";s:9:"Wholesale";s:17:"unique_identifier";s:9:"wholesale";s:7:"version";s:3:"1.8";s:9:"activated";i:1;s:5:"image";s:13:"wholesale.png";s:13:"purchase_code";s:36:"a1797b78-8ddf-4d6a-8f57-3333b2052aac";s:10:"created_at";s:19:"2024-09-10 12:04:35";s:10:"updated_at";s:19:"2024-09-10 12:04:35";}s:11:" * original";a:9:{s:2:"id";i:1;s:4:"name";s:9:"Wholesale";s:17:"unique_identifier";s:9:"wholesale";s:7:"version";s:3:"1.8";s:9:"activated";i:1;s:5:"image";s:13:"wholesale.png";s:13:"purchase_code";s:36:"a1797b78-8ddf-4d6a-8f57-3333b2052aac";s:10:"created_at";s:19:"2024-09-10 12:04:35";s:10:"updated_at";s:19:"2024-09-10 12:04:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}