<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class IsSeller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::check() && Auth::user()->user_type == 'seller'  && !Auth::user()->banned) {
            // Check if seller_unique_id is missing and generate it
            $user = Auth::user();
            if (empty($user->seller_unique_id)) {
                $user->seller_unique_id = generateSellerUniqueId();
                $user->save();
            }

            return $next($request);
        }
        else{
            abort(404);
        }
    }
}
