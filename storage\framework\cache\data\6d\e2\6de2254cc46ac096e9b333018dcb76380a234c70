9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:19:"App\Models\Category":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:21:"category_translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:17:{s:2:"id";i:4;s:9:"parent_id";i:0;s:5:"level";i:0;s:4:"name";s:9:"Furniture";s:11:"order_level";i:0;s:14:"commision_rate";d:0;s:6:"banner";s:3:"868";s:4:"icon";s:3:"868";s:11:"cover_image";s:3:"868";s:8:"featured";i:1;s:3:"top";i:0;s:7:"digital";i:0;s:4:"slug";s:15:"furniture-pddz5";s:10:"meta_title";N;s:16:"meta_description";N;s:10:"created_at";s:19:"2025-04-09 15:32:23";s:10:"updated_at";s:19:"2025-04-09 15:32:23";}s:11:" * original";a:17:{s:2:"id";i:4;s:9:"parent_id";i:0;s:5:"level";i:0;s:4:"name";s:9:"Furniture";s:11:"order_level";i:0;s:14:"commision_rate";d:0;s:6:"banner";s:3:"868";s:4:"icon";s:3:"868";s:11:"cover_image";s:3:"868";s:8:"featured";i:1;s:3:"top";i:0;s:7:"digital";i:0;s:4:"slug";s:15:"furniture-pddz5";s:10:"meta_title";N;s:16:"meta_description";N;s:10:"created_at";s:19:"2025-04-09 15:32:23";s:10:"updated_at";s:19:"2025-04-09 15:32:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:21:"category_translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:30:"App\Models\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:11:"category_id";i:4;s:4:"name";s:9:"Furniture";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-16 11:33:49";s:10:"updated_at";s:19:"2024-09-16 11:33:49";}s:11:" * original";a:6:{s:2:"id";i:4;s:11:"category_id";i:4;s:4:"name";s:9:"Furniture";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-16 11:33:49";s:10:"updated_at";s:19:"2024-09-16 11:33:49";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:4:"lang";i:2;s:11:"category_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:11:"bannerImage";O:17:"App\Models\Upload":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"uploads";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:868;s:18:"file_original_name";s:18:"Home furniture set";s:9:"file_name";s:56:"uploads/all/1uHZGmvez3IZPnMz55aqgpxU1T4U1HX8xR1qXE0Q.jpg";s:7:"user_id";i:9;s:9:"file_size";i:187106;s:9:"extension";s:3:"jpg";s:4:"type";s:5:"image";s:13:"external_link";N;s:10:"created_at";s:19:"2025-04-09 15:32:02";s:10:"updated_at";s:19:"2025-04-09 15:32:02";s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:868;s:18:"file_original_name";s:18:"Home furniture set";s:9:"file_name";s:56:"uploads/all/1uHZGmvez3IZPnMz55aqgpxU1T4U1HX8xR1qXE0Q.jpg";s:7:"user_id";i:9;s:9:"file_size";i:187106;s:9:"extension";s:3:"jpg";s:4:"type";s:5:"image";s:13:"external_link";N;s:10:"created_at";s:19:"2025-04-09 15:32:02";s:10:"updated_at";s:19:"2025-04-09 15:32:02";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:18:"file_original_name";i:1;s:9:"file_name";i:2;s:7:"user_id";i:3;s:9:"extension";i:4;s:4:"type";i:5;s:9:"file_size";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}