<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Product;

class UpdateSellerUniqueIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sellers:update-unique-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update existing sellers with unique IDs and their products with unique SKUs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting to update seller unique IDs and product SKUs...');

        // Update sellers without unique IDs
        $sellers = User::where('user_type', 'seller')
            ->whereNull('seller_unique_id')
            ->get();

        $this->info("Found {$sellers->count()} sellers without unique IDs");

        foreach ($sellers as $seller) {
            $uniqueId = generateSellerUniqueId();
            $seller->seller_unique_id = $uniqueId;
            $seller->save();
            
            $this->info("Updated seller {$seller->name} with ID: {$uniqueId}");

            // Update products for this seller
            $products = Product::where('user_id', $seller->id)
                ->whereNull('unique_sku')
                ->get();

            $this->info("  Found {$products->count()} products for seller {$uniqueId}");

            foreach ($products as $product) {
                $uniqueSku = generateProductUniqueSku($uniqueId);
                $product->unique_sku = $uniqueSku;
                $product->save();
                
                $this->info("    Updated product '{$product->name}' with SKU: {$uniqueSku}");
            }
        }

        // Update products for sellers who already have unique IDs but products don't have SKUs
        $sellersWithIds = User::where('user_type', 'seller')
            ->whereNotNull('seller_unique_id')
            ->get();

        foreach ($sellersWithIds as $seller) {
            $products = Product::where('user_id', $seller->id)
                ->whereNull('unique_sku')
                ->get();

            if ($products->count() > 0) {
                $this->info("Updating {$products->count()} products for seller {$seller->seller_unique_id}");

                foreach ($products as $product) {
                    $uniqueSku = generateProductUniqueSku($seller->seller_unique_id);
                    $product->unique_sku = $uniqueSku;
                    $product->save();
                    
                    $this->info("  Updated product '{$product->name}' with SKU: {$uniqueSku}");
                }
            }
        }

        $this->info('Successfully updated all seller unique IDs and product SKUs!');
        return 0;
    }
}
